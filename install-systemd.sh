#!/bin/bash

# GoLinks Systemd User Service Installation Script
# This script sets up the golinks service to run as a user service for "user"

set -e

# Configuration
TARGET_USER="user"
USER_HOME="/home/<USER>"
INSTALL_DIR="$USER_HOME/bin"
DATA_DIR="$USER_HOME/golinks-data"
SERVICE_FILE="golinks.service"
SYSTEMD_DIR="$USER_HOME/.config/systemd/user"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_user() {
    if [[ "$USER" != "$TARGET_USER" ]]; then
        log_error "This script must be run as user '$TARGET_USER'"
        log_info "Current user: $USER"
        exit 1
    fi

    if [[ ! -d "$USER_HOME" ]]; then
        log_error "User home directory $USER_HOME does not exist"
        exit 1
    fi
}

check_binary() {
    if [[ ! -f "./golinks" ]]; then
        log_error "golinks binary not found in current directory"
        log_info "Please build the binary first with: make && go build -o golinks ."
        exit 1
    fi
}

create_directories() {
    log_info "Creating directories"

    # Create systemd user directory
    mkdir -p "$SYSTEMD_DIR"

    # Create install directory
    mkdir -p "$INSTALL_DIR"

    # Create data directory
    mkdir -p "$DATA_DIR"

    log_info "Directories created successfully"
}

install_binary() {
    log_info "Installing golinks binary to $INSTALL_DIR"
    cp ./golinks "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/golinks"
}

install_service() {
    log_info "Installing systemd user service file"

    if [[ ! -f "$SERVICE_FILE" ]]; then
        log_error "Service file $SERVICE_FILE not found"
        exit 1
    fi

    cp "$SERVICE_FILE" "$SYSTEMD_DIR/"
    systemctl --user daemon-reload
}

configure_firewall() {
    log_warn "Firewall configuration"
    log_info "You may need to configure your firewall to allow connections on port 8067"
    log_info "For UFW: sudo ufw allow 8067"
    log_info "For firewalld: sudo firewall-cmd --permanent --add-port=8067/tcp && sudo firewall-cmd --reload"
}

main() {
    log_info "Starting GoLinks systemd user service installation"

    check_user
    check_binary
    create_directories
    install_binary
    install_service

    log_info "Installation completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Start the service: systemctl --user start golinks"
    log_info "2. Enable auto-start: systemctl --user enable golinks"
    log_info "3. Check status: systemctl --user status golinks"
    log_info "4. View logs: journalctl --user -u golinks -f"
    log_info "5. Enable lingering (optional): sudo loginctl enable-linger $TARGET_USER"
    log_info ""
    configure_firewall
    log_info ""
    log_info "The service will be available at http://localhost:8067"
    log_info "Service will run as user '$TARGET_USER' without requiring root privileges"
}

main "$@"
