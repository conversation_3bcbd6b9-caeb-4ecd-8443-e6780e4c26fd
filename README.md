# A "go" short-link service

## Background
The first time I encountered "go" links was at Google. Anyone on the corporate
network could register a URL shortcut and it would redirect the user to the
appropriate page. So for instance, if you wanted to find out more about BigTable,
you simply directed your browser at http://go/bigtable and you would be redirected to
something about the BigTable data storage system. I was later told that the
first go service at Google was written by [<PERSON>](https://www.linkedin.com/in/benjaminstaffin)
to end the never-ending stream of requests for internal CNAME entries. He
described it as AOL keywords for the corporate network. These days if you go to
any reasonably sized company, you are likely to find a similar system. <PERSON><PERSON><PERSON> made
one after seeing that Twitter had one ... it's a contagious and useful little
tool. So contagious, in fact, that many former Googlers that I know have built
or contributed to a similar system post-Google. I am no different, this is my
"go" link service.

One slight difference between this go service and Google's is that this one is also
capable of generating short links for you.

## Prerequisites
Before building this project, you need to install the following dependencies:

### Required Software
- **Go** (version 1.16 or later) - [Download Go](https://golang.org/dl/)
- **Node.js and npm** - [Download Node.js](https://nodejs.org/) (includes npm)
- **Sass** - CSS preprocessor
- **TypeScript** - JavaScript compiler
- **Google Closure Compiler** - JavaScript minifier

### Installing Dependencies

1. **Install Go**: Follow the instructions at https://golang.org/dl/

2. **Install Node.js and npm**: Follow the instructions at https://nodejs.org/

3. **Install Sass**:
   ```bash
   npm install -g sass
   ```

4. **Install TypeScript**:
   ```bash
   npm install -g typescript
   ```

5. **Install Google Closure Compiler**:
   ```bash
   npm install -g google-closure-compiler
   ```

6. **Install go-bindata** (for embedding assets):
   ```bash
   go install github.com/shuLhan/go-bindata/cmd/go-bindata@latest
   ```

## Installation
This tool is written in Go (ironically) and can be built and started with the following commands:

```bash
# Clone the repository
git clone <repository-url>
cd golinks

# Build the assets and application
make

# Build the Go binary
go build -o golinks .

# Run the service
./golinks
```

By default, the service will put all of its data in the directory `data` and will
listen to requests on the port `8067`. Both of these, however, are easily configured
using the `--data=/path/to/data` and `--addr=:80` command line flags.

## Build Process
The build process uses a Makefile that performs the following steps:

1. **TypeScript Compilation**: Compiles `.ts` files to JavaScript using `tsc`
2. **JavaScript Minification**: Minifies JavaScript using Google Closure Compiler
3. **Sass Compilation**: Compiles `.scss` files to CSS using `sass`
4. **Asset Embedding**: Embeds all web assets into Go using `go-bindata`
5. **Go Compilation**: Builds the final binary

### Build Commands
```bash
# Clean previous build artifacts
make clean

# Build all assets and generate bindata.go
make

# Build the Go application
go build -o golinks .

# Run tests
go test ./...
```

### Troubleshooting
If you encounter build errors:

1. **"command not found" errors**: Ensure all dependencies are installed and in your PATH
2. **TypeScript errors**: Check that you have TypeScript installed globally (`npm list -g typescript`)
3. **Sass errors**: Verify Sass installation (`sass --version`)
4. **Go module errors**: Run `go mod tidy` to resolve dependencies

## DNS Setup
To get the most benefit from the service, you should setup a DNS entry on your
local network, `go.corp.mycompany.com`. Make sure that corp.mycompany.com is in
the search domains for each user on the network. This is usually easily accomplished
by configuring your DHCP server. Now, simply typing "go" into your browser should
take you to the service, where you can register shortcuts. Obviously, those
shortcuts will also be available by typing "go/shortcut".

## Using the Service
Once you have it all setup, using it is pretty straight-forward.

#### Create a new shortcut
Type `go/edit/my-shortcut` and enter the URL.

#### Visit a shortcut
Type `go/my-shortcut` and you'll be redirected to the URL.

#### Shorten a URL
Type `go` and enter the URL.

## Dump Loader Utility

The `dump-loader` utility allows you to bulk import golinks from a JSON dump file into a running golinks service.

### Building the dump-loader
```bash
# Build just the dump-loader utility
make dump-loader

# Or build everything including dump-loader
make
```

### Using the dump-loader
```bash
# Load links from a JSON dump file to localhost:8067
./dump-loader -file /path/to/dump.json

# Load links to a different host/port
./dump-loader -host example.com -port 8080 -file /path/to/dump.json
```

### JSON Format
The dump file should be a JSON object where keys are the short link names and values contain the URL data:
```json
{
  "example": {
    "url": "https://example.com",
    "ts": "2023-01-01T00:00:00Z"
  },
  "google": {
    "url": "https://google.com",
    "ts": "2023-01-01T00:00:00Z"
  }
}
```
