[Unit]
Description=GoLinks URL Shortener Service
Documentation=https://gitlab.ad.astraspace.com/tss/golinks
After=network.target
Wants=network.target

[Service]
Type=simple

# Working directory and binary location
WorkingDirectory=%h/golinks
ExecStart=%h/bin/golinks --data=%h/golinks-data

# Restart policy
Restart=always
RestartSec=5
StartLimitInterval=60s
StartLimitBurst=3

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=golinks

# Environment
Environment=HOME=%h
Environment=USER=user

[Install]
WantedBy=default.target
