var xhr;
(function (xhr_1) {
    var Req = /** @class */ (function () {
        function Req(xhr) {
            var _this = this;
            this.xhr = xhr;
            this.doneFns = [];
            this.errorFns = [];
            xhr.onload = function () {
                var text = xhr.responseText, status = xhr.status;
                _this.doneFns.forEach(function (fn) {
                    fn(text, status);
                });
            };
            xhr.onerror = function () {
                _this.errorFns.forEach(function (fn) { return fn(); });
            };
        }
        Req.prototype.onDone = function (fn) {
            this.doneFns.push(fn);
            return this;
        };
        Req.prototype.onError = function (fn) {
            this.errorFns.push(fn);
            return this;
        };
        Req.prototype.withHeader = function (k, v) {
            this.xhr.setRequestHeader(k, v);
            return this;
        };
        Req.prototype.sendJSON = function (data) {
            this.withHeader('Content-Type', 'application/json;charset=utf8');
            this.xhr.send(JSON.stringify(data));
            return this;
        };
        Req.prototype.send = function (data) {
            this.xhr.send(data);
            return this;
        };
        return Req;
    }());
    xhr_1.Req = Req;
    xhr_1.create = function (method, url) {
        var xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        return new Req(xhr);
    };
    xhr_1.get = function (url) {
        return xhr_1.create('GET', url);
    };
    xhr_1.post = function (url) {
        return xhr_1.create('POST', url);
    };
})(xhr || (xhr = {}));
