@import "lib/global";

form {
  text-align: center;
}

#bar {
  width: 652px;
  margin: 0 auto;
  position: relative;
}

#url {
  font-family: 'Raleway', sans-serif;
  font-size: 32px;
  font-weight: 300;
  width: 600px;
  padding: 25px;
  color: #999;
  border-radius: 4px;
  border: 1px solid #ccc;
  outline: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

#url:focus {
  border: 1px solid #09f;
}

#url::-webkit-input-placeholder {
   color: #ddd;
}

#url::-moz-placeholder {
   color: #ddd;
}

#cmp {
  padding: 25px;
  width: 560px;
  margin: 0 auto;
  text-align: left;
  font-size: 21px;
  transition: transform 200ms ease-in-out;
  transform: scaleY(0);
  transform-origin: top center;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border: 1px solid #ccc;
}

#cmp.link {
  background-color: #f6f6f6;
}

#cmp.fuck {
  background-color: #fee;
  color: #999;
}

#cmp > a {
  color: #09f;
  text-decoration: none;
}

#cmp > .hnt {
  color: #ddd;
  text-shadow: 1px 1px 0 #fff;
}

#cmp > .hnt {
  float: right;
}

#cls {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 64px;
  background-image: url("/s/close.svg");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 48px 48px;
  cursor: pointer;
  transition: opacity 200ms cubic-bezier(0.77, 0, 0.175, 1);
  opacity: 0;
  pointer-events: none;
}
#cls.vis {
  opacity: 0.3;
  pointer-events: inherit;
}

#cls:hover {
  opacity: 0.6;
}


#help {
  padding: 25px;
  width: 560px;
  margin: 0 auto;
  text-align: left;
  font-size: 21px;
  // transition: transform 200ms ease-in-out;
  // transform: scaleY(0);
  // transform-origin: top center;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  border: 1px solid #ccc;
}

#help.link {
  background-color: #f6f6f6;
}
