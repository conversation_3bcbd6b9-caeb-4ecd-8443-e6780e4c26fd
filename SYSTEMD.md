# GoLinks Systemd User Service Setup

This document describes how to set up the GoLinks service as a systemd user service for the "user" account.

## Quick Start

1. Build the GoLinks binary:
   ```bash
   make
   go build -o golinks .
   ```

2. Run the installation script as the "user" account:
   ```bash
   ./install-systemd.sh
   ```

3. Start and enable the service:
   ```bash
   systemctl --user start golinks
   systemctl --user enable golinks
   ```

## Manual Installation

If you prefer to install manually, follow these steps as the "user" account:

### 1. Create Directories

```bash
mkdir -p ~/.config/systemd/user
mkdir -p ~/golinks
mkdir -p ~/.local/share/golinks
```

### 2. Install Binary

```bash
cp ./golinks ~/golinks/
chmod +x ~/golinks/golinks
```

### 3. Install Service File

```bash
cp golinks.service ~/.config/systemd/user/
systemctl --user daemon-reload
```

## Service Configuration

The systemd user service is configured with the following key features:

### Security Hardening
- Runs as regular user `user` (no root privileges required)
- Restricted filesystem access (`ProtectSystem=strict`)
- Private temporary directory (`PrivateTmp=true`)
- No new privileges (`NoNewPrivileges=true`)
- Restricted network access (localhost and private networks only)
- No capabilities required

### Resource Management
- File descriptor limit: 65536
- Process limit: 4096
- Automatic restart on failure
- Rate limiting for restart attempts

### Data Storage
- Data directory: `~/.local/share/golinks`
- Binary location: `~/golinks/golinks`
- Logs: Available via `journalctl --user -u golinks`

## Service Management

### Start/Stop/Restart
```bash
systemctl --user start golinks
systemctl --user stop golinks
systemctl --user restart golinks
```

### Enable/Disable Auto-start
```bash
systemctl --user enable golinks
systemctl --user disable golinks
```

### Check Status
```bash
systemctl --user status golinks
```

### View Logs
```bash
# Follow logs in real-time
journalctl --user -u golinks -f

# View recent logs
journalctl --user -u golinks -n 50

# View logs since boot
journalctl --user -u golinks -b
```

### Enable Lingering (Optional)
To allow the service to start at boot without the user being logged in:
```bash
sudo loginctl enable-linger user
```

## Configuration Options

The service can be customized by editing `~/.config/systemd/user/golinks.service`:

### Change Port
```ini
ExecStart=%h/golinks/golinks --data=%h/.local/share/golinks --addr=:8080
```

### Enable Admin Features
```ini
ExecStart=%h/golinks/golinks --data=%h/.local/share/golinks --addr=:8067 --admin
```

### Change Data Directory
```ini
ExecStart=%h/golinks/golinks --data=%h/custom-data --addr=:8067
ExecStartPre=/bin/mkdir -p %h/custom-data
```

After making changes, reload and restart:
```bash
systemctl --user daemon-reload
systemctl --user restart golinks
```

## Network Configuration

### Firewall
The service listens on port 8067 by default. Configure your firewall:

**UFW:**
```bash
sudo ufw allow 8067
```

**firewalld:**
```bash
sudo firewall-cmd --permanent --add-port=8067/tcp
sudo firewall-cmd --reload
```

### Network Access
The service is configured to only accept connections from:
- localhost (127.0.0.1)
- Private networks (10.0.0.0/8, **********/12, ***********/16)

To allow broader access, modify the `IPAddressAllow` settings in the service file.

## Troubleshooting

### Service Won't Start
1. Check the service status:
   ```bash
   systemctl --user status golinks
   ```

2. Check logs for errors:
   ```bash
   journalctl --user -u golinks -n 20
   ```

3. Verify binary permissions:
   ```bash
   ls -la ~/golinks/golinks
   ```

4. Check data directory permissions:
   ```bash
   ls -la ~/.local/share/golinks/
   ```

### Permission Denied Errors
Ensure the user owns the data directory:
```bash
chmod -R u+rw ~/.local/share/golinks
```

### Port Already in Use
Check what's using port 8067:
```bash
sudo netstat -tlnp | grep 8067
```

### Service Keeps Restarting
Check logs for the root cause:
```bash
journalctl --user -u golinks -f
```

Common issues:
- Binary not found or not executable
- Data directory not writable
- Port already in use
- Network configuration issues

## Backup and Maintenance

### Backup Data
The service data is stored in `~/.local/share/golinks`. To backup:
```bash
tar -czf golinks-backup-$(date +%Y%m%d).tar.gz -C ~/.local/share golinks
```

### Update Binary
1. Stop the service:
   ```bash
   systemctl --user stop golinks
   ```

2. Replace the binary:
   ```bash
   cp ./golinks ~/golinks/
   ```

3. Start the service:
   ```bash
   systemctl --user start golinks
   ```

## Uninstallation

To remove the GoLinks user service:

```bash
# Stop and disable service
systemctl --user stop golinks
systemctl --user disable golinks

# Remove service file
rm ~/.config/systemd/user/golinks.service
systemctl --user daemon-reload

# Remove binary and directories
rm -rf ~/golinks
rm -rf ~/.local/share/golinks

# Disable lingering (if enabled)
sudo loginctl disable-linger user
```
